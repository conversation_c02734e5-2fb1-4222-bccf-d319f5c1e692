<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Inicializar variables importantes
$es_admin = false; // Variable que indica si el usuario es administrador

// Aplicar headers anti-caché
no_cache_headers();

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Verificar que el usuario pertenezca al proyecto InteletGroup
if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    error_log("[" . date('Y-m-d H:i:s') . "] Acceso denegado - Usuario no pertenece al proyecto InteletGroup - Usuario: " . $_SESSION['usuario']);
    $errorMsg = urlencode("Acceso denegado. No tiene permisos para acceder a esta sección.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir el archivo de conexión
require_once 'con_db.php';

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Panel de Control</title>
    <?php echo no_cache_meta(); ?>
    <!-- <link rel="stylesheet" href="<?php echo version_url('css/form_experian.css'); ?>"> -->
    <!-- Estilos para tablas ordenables -->
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <!-- Estilos para formulario de prospectos InteletGroup -->
    <link rel="stylesheet" href="<?php echo version_url('css/inteletgroup-prospect.css'); ?>">
    <!-- Estilos para checklist de documentos -->
    <link rel="stylesheet" href="<?php echo version_url('css/inteletgroup-checklist.css'); ?>">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <style>
        /* Variables de color mejoradas para mejor contraste y armonía */
        :root {
            /* Colores principales - Paleta cohesiva */
            --primary-blue: #2699FB;
            --primary-dark: #1e3a8a;
            --primary-medium: #3b82f6;
            --primary-light: #60a5fa;

            /* Header con gradiente profesional */
            --header-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #2563eb 100%);
            --header-overlay: rgba(30, 58, 138, 0.95);

            /* Colores de estado */
            --success-color: #10b981;
            --success-light: #34d399;
            --info-color: #06b6d4;
            --info-light: #22d3ee;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --secondary-color: #6b7280;
            --secondary-light: #9ca3af;

            /* Colores neutros */
            --light-color: #f8fafc;
            --light-gray: #f1f5f9;
            --medium-gray: #e2e8f0;
            --dark-color: #1f2937;
            --dark-medium: #374151;
            --body-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

            /* Efectos y sombras */
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease-out;

            /* Colores específicos de tarjetas */
            --card-green-border: var(--success-color);
            --card-blue-border: var(--info-color);
            --card-yellow-border: var(--warning-color);
            --card-gray-border: var(--secondary-color);
        }
        
        /* Header profesional con gradiente mejorado */
        .simple-header {
            background: var(--header-gradient);
            color: white;
            box-shadow: var(--box-shadow-lg);
            margin-bottom: 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0;
            backdrop-filter: blur(10px);
        }

        /* Overlay para mejorar contraste */
        .simple-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--header-overlay);
            z-index: -1;
        }

        /* Contenedor principal del header */
        .header-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
            position: relative;
            z-index: 1;
        }

        /* Logo y nombre de la empresa */
        .brand-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Logo wrapper mejorado */
        .logo-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .logo-wrapper:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }

        .logo-wrapper img {
            height: 28px;
            width: 28px;
            object-fit: contain;
            border-radius: 50%;
        }

        /* Fallback icon si no hay imagen */
        .logo-wrapper::after {
            content: '🏢';
            font-size: 1.5rem;
            display: none;
        }

        .logo-wrapper img[style*="display: none"] + ::after {
            display: block;
        }

        /* Estilos para la información del sitio */
        .site-info {
            display: flex;
            flex-direction: column;
        }

        .site-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            padding: 0;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: -0.025em;
        }

        .site-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
            padding: 0;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        /* Usuario y acciones */
        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Estilos para el nombre de usuario */
        .user-info-container {
            text-align: right;
            line-height: 1.3;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .user-name {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .user-role {
            font-size: 0.8rem;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-weight: 500;
        }

        /* Botón de logout mejorado */
        .logout-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem;
            font-size: 1rem;
            border-radius: 50%;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            text-decoration: none;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .logout-btn:active {
            transform: scale(0.95);
        }
        
        /* Estilos base del body */
        body {
            background: var(--body-bg);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* Estilos para cards y componentes modernos */
        .welcome-card {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-medium) 50%, var(--primary-light) 100%);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow-lg);
            overflow: hidden;
            position: relative;
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        /* Tarjetas de funcionalidades mejoradas */
        .feature-card {
            transition: var(--transition);
            border: none;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, transparent, var(--primary-medium), transparent);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 2rem;
            text-align: center;
        }

        .feature-card .card-text {
            flex-grow: 1;
            margin-bottom: 1.5rem;
            color: var(--dark-medium);
            line-height: 1.6;
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--box-shadow-lg);
        }

        /* Estilos específicos para cada tipo de tarjeta */
        .feature-card.green-card {
            border-left: 4px solid var(--success-color);
        }

        .feature-card.green-card:hover {
            border-left-color: var(--success-light);
        }

        .feature-card.blue-card {
            border-left: 4px solid var(--info-color);
        }

        .feature-card.blue-card:hover {
            border-left-color: var(--info-light);
        }

        .feature-card.yellow-card {
            border-left: 4px solid var(--warning-color);
        }

        .feature-card.yellow-card:hover {
            border-left-color: var(--warning-light);
        }

        .feature-card.gray-card {
            border-left: 4px solid var(--secondary-color);
        }

        .feature-card.gray-card:hover {
            border-left-color: var(--secondary-light);
        }

        /* Íconos de tarjetas mejorados */
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin-left: auto;
            margin-right: auto;
            transition: var(--transition);
            position: relative;
        }

        .green-card .feature-icon {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(52, 211, 153, 0.1));
            color: var(--success-color);
            border: 2px solid rgba(16, 185, 129, 0.2);
        }

        .blue-card .feature-icon {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(34, 211, 238, 0.1));
            color: var(--info-color);
            border: 2px solid rgba(6, 182, 212, 0.2);
        }

        .yellow-card .feature-icon {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
            color: var(--warning-color);
            border: 2px solid rgba(245, 158, 11, 0.2);
        }

        .gray-card .feature-icon {
            background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(156, 163, 175, 0.1));
            color: var(--secondary-color);
            border: 2px solid rgba(107, 114, 128, 0.2);
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--box-shadow);
        }
        
        /* Tarjetas próximamente */
        .coming-soon {
            opacity: 0.8;
            position: relative;
            filter: grayscale(0.2);
        }

        .coming-soon::after {
            content: "Próximamente";
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: var(--box-shadow);
            z-index: 2;
        }

        .coming-soon .btn {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Botones mejorados */
        .btn {
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), var(--success-light));
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-color), var(--info-light));
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
        }
        
        /* Estilos para notificaciones y botones modernos */
        #inteletgroup-notifications-container {
            z-index: 9999;
            position: fixed;
            top: 70px;
            right: 20px;
            max-width: 350px;
        }
        
        #inteletgroup-notifications-container .alert {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 6px;
            border-left: 4px solid;
            margin-bottom: 10px;
            padding: 15px;
            animation: slideInRight 0.3s ease-out;
        }
        
        #inteletgroup-notifications-container .alert-success {
            border-left-color: var(--success-color);
            background-color: rgba(40, 167, 69, 0.05);
        }
        
        #inteletgroup-notifications-container .alert-danger {
            border-left-color: var(--error-color);
            background-color: rgba(220, 53, 69, 0.05);
        }
        
        #inteletgroup-notifications-container .alert-info {
            border-left-color: var(--info-color);
            background-color: rgba(23, 162, 184, 0.05);
        }
        
        /* Animaciones de botones y elementos interactivos */
        .btn {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn:active {
            transform: scale(0.97);
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%, -50%);
            transform-origin: 50% 50%;
        }
        
        .btn:focus:not(:active)::after {
            animation: ripple 0.8s ease-out;
        }
        
        /* Estilos para mensajes en el modal */
        #inteletgroup-message-container .alert {
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            animation: fadeInDown 0.5s;
            border-left: 4px solid;
            padding: 15px;
        }
        
        #inteletgroup-message-container .alert-success {
            border-left-color: var(--success-color);
            background-color: rgba(40, 167, 69, 0.05);
        }
        
        #inteletgroup-message-container .alert-danger {
            border-left-color: var(--error-color);
            background-color: rgba(220, 53, 69, 0.05);
        }
        
        #inteletgroup-message-container .alert-info {
            border-left-color: var(--info-color);
            background-color: rgba(23, 162, 184, 0.05);
        }
        
        /* Animaciones */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: scale(25, 25);
                opacity: 0.5;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header-container {
                padding: 0.75rem 0;
            }

            .brand-section {
                gap: 0.75rem;
            }

            .site-title {
                font-size: 1.25rem;
            }

            .site-subtitle {
                font-size: 0.8rem;
            }

            .user-info-container {
                padding: 0.5rem 0.75rem;
            }

            .user-name {
                font-size: 0.9rem;
            }

            .user-role {
                font-size: 0.75rem;
            }

            .feature-card .card-body {
                padding: 1.5rem;
            }

            .feature-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .user-info-container {
                display: none;
            }

            .site-title {
                font-size: 1.1rem;
            }

            .site-subtitle {
                font-size: 0.75rem;
            }
        }

        /* Aplicar animaciones */
        .welcome-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .feature-card {
            animation: fadeInUp 0.6s ease-out;
            animation-fill-mode: both;
        }

        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }
        .feature-card:nth-child(4) { animation-delay: 0.4s; }
        .feature-card:nth-child(5) { animation-delay: 0.5s; }
        .feature-card:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <!-- Header profesional inspirado en la captura de pantalla -->
    <header class="simple-header">
        <div class="container">
            <div class="header-container">
                <!-- Logo y nombre del sitio -->
                <div class="brand-section">
                    <div class="logo-wrapper">
                        <img src="img/icons/logo_intelet.jpg" alt="Logo InteletGroup"
                             onerror="this.innerHTML='<i class=\'bi bi-building\' style=\'font-size: 1.5rem; color: white;\'></i>'">
                    </div>
                    <div class="site-info">
                        <h1 class="site-title">InteletGroup</h1>
                        <span class="site-subtitle">Panel Corporativo</span>
                    </div>
                </div>
                
                <!-- Usuario y acciones -->
                <div class="user-section">
                    <div class="user-info-container">
                        <div class="user-name"><?php echo htmlspecialchars($nombre_usuario); ?></div>
                        <div class="user-role"><?php echo htmlspecialchars($proyecto); ?></div>
                    </div>
                    <a href="logout.php" class="logout-btn" title="Cerrar sesión">
                        <i class="bi bi-box-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Welcome Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card welcome-card">
                    <div class="card-body text-center py-5">
                        <h2 class="card-title mb-3">
                            <i class="bi bi-rocket-takeoff me-2"></i>
                            Bienvenido a InteletGroup
                        </h2>
                        <p class="card-text lead">
                            Tu plataforma integral para la gestión de clientes y prospectos
                        </p>

                        <?php if (isset($_GET['dev']) && $_GET['dev'] === 'true'): ?>
                        <div class="mt-4">
                            <a href="limpiar_datos_prueba.php" class="btn btn-warning btn-sm">
                                <i class="bi bi-tools me-2"></i>
                                Herramientas de Desarrollo
                            </a>
                            <small class="d-block mt-2 text-light opacity-75">
                                Modo desarrollador activado
                            </small>
                        </div>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>

           <!-- Information Alert -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <h4 class="alert-heading">
                        <i class="bi bi-info-circle me-2"></i>
                        Información Importante
                    </h4>
                    <p>
                        Bienvenido al nuevo panel de InteletGroup. Estamos trabajando en el desarrollo de nuevas 
                        funcionalidades específicas para tu proyecto. 
                    </p>
                    <hr>
                    <p class="mb-0">
                        <strong>Fecha de lanzamiento estimada:</strong> Hoy<br>
                        <strong>Contacto para consultas:</strong> <EMAIL>
                    </p>
                </div>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="row g-4">
            <!-- Registro de Prospectos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card green-card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-person-plus-fill"></i>
                        </div>
                        <h5 class="card-title fw-bold">Registro de Prospectos</h5>
                        <p class="card-text">
                            Registra nuevos prospectos comerciales con toda su información y documentación.
                        </p>
                        <button class="btn btn-success w-100" onclick="abrirModalInteletGroupProspecto()">
                            <i class="bi bi-plus-circle me-1"></i> Nuevo Prospecto
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gestión de Documentos -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card blue-card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-file-earmark-arrow-up"></i>
                        </div>
                        <h5 class="card-title fw-bold">Gestión de Documentos</h5>
                        <p class="card-text">
                            Sube y gestiona documentos de prospectos existentes por RUT.
                        </p>
                        <a href="inteletgroup_documentos_enhanced.php" class="btn btn-info w-100">
                            <i class="bi bi-files me-1"></i> Gestionar Docs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Reportes -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card blue-card coming-soon">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <h5 class="card-title fw-bold">Reportes y Analytics</h5>
                        <p class="card-text">
                            Obtén insights valiosos con reportes detallados y análisis de datos.
                        </p>
                        <button class="btn btn-info w-100" disabled>
                            <i class="bi bi-arrow-right me-1"></i> Acceder
                        </button>
                    </div>
                </div>
            </div>

            <!-- Configuración -->
            <div class="col-md-6 col-lg-6">
                <div class="card feature-card yellow-card coming-soon">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-gear-fill"></i>
                        </div>
                        <h5 class="card-title fw-bold">Configuración</h5>
                        <p class="card-text">
                            Personaliza tu experiencia y configura las preferencias del sistema.
                        </p>
                        <button class="btn btn-warning w-100" disabled>
                            <i class="bi bi-arrow-right me-1"></i> Acceder
                        </button>
                    </div>
                </div>
            </div>

            <!-- Soporte -->
            <div class="col-md-6 col-lg-6">
                <div class="card feature-card gray-card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="bi bi-headset"></i>
                        </div>
                        <h5 class="card-title fw-bold">Soporte Técnico</h5>
                        <p class="card-text">
                            ¿Necesitas ayuda? Contacta con nuestro equipo de soporte técnico.
                        </p>
                        <a href="mailto:<EMAIL>" class="btn btn-secondary w-100">
                            <i class="bi bi-envelope me-1"></i> Contactar
                        </a>
                    </div>
                </div>
            </div>

           
        </div>

     
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 bg-light">
        <div class="container text-center">
            <p class="mb-0 text-muted">
                &copy; <?php echo date('Y'); ?> Gestar servicios. Todos los derechos reservados.
            </p>
        </div>
    </footer>

    <!-- Modal del formulario de prospectos -->
    <?php include 'inteletgroup_prospect_modal.html'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript del formulario de prospectos -->
    <script src="js/inteletgroup-prospect.js?v=<?php echo time(); ?>"></script>

    <script>
        // Variables globales para el formulario
        window.currentUserName = '<?php echo addslashes($nombre_usuario); ?>';
        window.currentUserId = <?php echo $usuario_id; ?>;

        // Log del proyecto para debugging
        console.log('InteletGroup Panel - Usuario:', '<?php echo htmlspecialchars($nombre_usuario); ?>');
        console.log('Proyecto:', '<?php echo htmlspecialchars($proyecto); ?>');
        console.log('Usuario ID:', <?php echo $usuario_id; ?>);

        // Mensaje de bienvenida y inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Panel de InteletGroup cargado correctamente');
        });
    </script>
</body>
</html>
